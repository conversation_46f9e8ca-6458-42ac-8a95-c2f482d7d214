import requests
import time
import urllib3
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.alert import Alert
from fake_useragent import UserAgent
import random
import os
import threading
import subprocess
import uuid
import sys


# تعطيل تحذيرات SSL الغير مهمة
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# دالة محسّنة للحصول على UUID الحقيقي للجهاز
def get_hardware_id():
    try:
        command = 'powershell "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"'
        result = subprocess.check_output(command, shell=True, stderr=subprocess.DEVNULL)
        hardware_id = result.decode().strip()
        if hardware_id and hardware_id != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF":
            return hardware_id
    except Exception:
        pass
    return str(uuid.getnode())

# دالة التحقق من الترخيص والأجهزة المصرح بها من السيرفر
def verify_license_and_device():
    try:
        device_id = get_hardware_id()

        # إعداد هيدرز متقدمة لتجنب رفض الطلبات
        ua = UserAgent()
        headers = {
            'User-Agent': ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Pragma': 'no-cache'
        }

        # Server connection with retry
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(
                    'https://yz9cz.github.io/verify_api/config.json',
                    headers=headers,
                    timeout=10,
                    verify=False,
                    allow_redirects=True
                )

                if response.status_code == 200:
                    config = response.json()
                    break
                else:
                    if attempt == max_retries - 1:
                        raise Exception("Connection failed")
                    time.sleep(2)

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise Exception("Connection failed")
                time.sleep(2)

        # Check general activation status
        if not config.get('is_active', False):
            print(config.get('message', 'Program is currently unavailable'))
            sys.exit()

        # Check license expiry date
        expiry = config.get('expiry_date')
        if expiry:
            try:
                expiry_date = datetime.strptime(expiry, "%Y-%m-%d").date()
                today = datetime.now().date()
                if today > expiry_date:
                    print(config.get('message', 'Program access has expired'))
                    sys.exit()
            except ValueError:
                print(config.get('message', 'Configuration error occurred'))
                sys.exit()

        # Check authorized devices
        authorized_devices = config.get('authorized_devices', [])
        if not authorized_devices:
            print(config.get('message', 'No authorized access found'))
            sys.exit()

        device_authorized = False
        for authorized_device in authorized_devices:
            if device_id.lower() == authorized_device.lower():
                device_authorized = True
                break

        if not device_authorized:
            print(config.get('unauthorized_device_message', 'This device is not authorized. Please contact support for access.'))
            sys.exit()

        # Silent initialization - no success messages

        return config

    except Exception as e:
        print("This device is not authorized. Please contact support for access.")
        sys.exit()

# دالة للتحقق من حالة الإيقاف من السيرفر أثناء التشغيل
def check_server_status():
    try:
        # التحقق من حالة تفعيل فحص السيرفر
        is_active_expiry = server_config.get('is_active_expiry_date', True)
        if not is_active_expiry:
            # فحص السيرفر معطل، نستمر في التشغيل بدون فحص
            return True

        ua = UserAgent()
        headers = {
            'User-Agent': ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        }

        response = requests.get(
            'https://yz9cz.github.io/verify_api/config.json',
            headers=headers,
            timeout=5,
            verify=False
        )

        if response.status_code == 200:
            config = response.json()

            # التحقق من حالة تفعيل فحص السيرفر من الاستجابة الجديدة
            is_active_expiry_new = config.get('is_active_expiry_date', True)
            if not is_active_expiry_new:
                # فحص السيرفر معطل، نستمر في التشغيل بدون فحص
                return True

            if not config.get('is_active', False):
                print(config.get('message', 'Program is currently unavailable'))
                return False
        return True
    except:
        # في حالة فشل الاتصال، نستمر في التشغيل
        return True

# Run initial verification
server_config = verify_license_and_device()

# API
url = "https://menfax.com/pos/public/api/orders"
status_url = "https://menfax.com/pos/public/api/change-order-status"
secret = "SECRET1265AQREFGHKLFS!@#"

log_lock = threading.Lock()

# Enhanced logging system with detailed tracking
def log(message, log_type="INFO"):
    """
    Enhanced logging function with multiple log files and detailed tracking
    Args:
        message: The message to log
        log_type: Type of log (INFO, ERROR, SUCCESS, WARNING, PAYMENT)
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    full_message = f"[{timestamp}] [{log_type}] {message}"

    # Print to console in English
    print(full_message)

    # Write to main log file
    with log_lock:
        with open("main_log.txt", "a", encoding="utf-8") as f:
            f.write(full_message + "\n")

        # Write to specific log files based on type
        if log_type == "ERROR":
            with open("error_log.txt", "a", encoding="utf-8") as f:
                f.write(full_message + "\n")
        elif log_type == "PAYMENT":
            with open("payment_log.txt", "a", encoding="utf-8") as f:
                f.write(full_message + "\n")
        elif log_type == "SUCCESS":
            with open("success_log.txt", "a", encoding="utf-8") as f:
                f.write(full_message + "\n")

def log_payment_details(order_id, phone_number, amount, status, details=""):
    """
    Log detailed payment information to a dedicated payment tracking file
    Args:
        order_id: Order ID
        phone_number: Customer phone number
        amount: Payment amount
        status: Payment status (1=paid, 2=number not found, 3=payment failed)
        details: Additional details about the payment
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    payment_entry = f"{timestamp}|{order_id}|{phone_number}|{amount}|{status}|{details}\n"

    with log_lock:
        with open("payment_tracking.txt", "a", encoding="utf-8") as f:
            f.write(payment_entry)

def check_duplicate_payment(phone_number, amount):
    """
    Check if the same amount was paid for the same number within 24 hours
    Args:
        phone_number: Customer phone number
        amount: Payment amount
    Returns:
        bool: True if duplicate found, False otherwise
    """
    if not os.path.exists("payment_tracking.txt"):
        log("No payment tracking file found - no duplicates possible", "INFO")
        return False

    current_time = datetime.now()
    twenty_four_hours_ago = current_time - timedelta(hours=24)

    try:
        with open("payment_tracking.txt", "r", encoding="utf-8") as f:
            for line in f:
                parts = line.strip().split("|")
                if len(parts) >= 5:
                    payment_time = datetime.strptime(parts[0], "%Y-%m-%d %H:%M:%S")
                    payment_phone = parts[2]
                    payment_amount = parts[3]
                    payment_status = parts[4]

                    # Check if payment was successful and within 24 hours
                    if (payment_time >= twenty_four_hours_ago and
                        payment_phone == phone_number and
                        payment_amount == amount and
                        payment_status == "1"):
                        log(f"Duplicate payment found: {phone_number} - {amount} at {parts[0]}", "WARNING")
                        return True

        log(f"No duplicate payment found for {phone_number} - {amount} within 24 hours", "INFO")
    except Exception as e:
        log(f"Error checking duplicate payments: {e}", "ERROR")

    return False

def random_sleep(min_sec=0.5, max_sec=1.5):
    """
    Sleep for a random duration between min_sec and max_sec seconds
    Args:
        min_sec: Minimum sleep duration
        max_sec: Maximum sleep duration
    """
    time.sleep(random.uniform(min_sec, max_sec))

def extract_debt_amount(balance_text):
    """
    Extract debt amount from Arabic balance text using multiple patterns
    Args:
        balance_text: Arabic text like "رصيد المشترك 23999.21ل. س عليه" or "رصيد المشترك 3300.42ل. س عليه"
    Returns:
        float: Debt amount or -1 if extraction fails (to indicate parsing error)
    """
    try:
        import re

        # Pattern 1: Numbers before "ل. س" (with space)
        pattern1 = r'(\d+(?:\.\d+)?)\s*ل\.\s*س'
        match1 = re.search(pattern1, balance_text)
        if match1:
            return float(match1.group(1))

        # Pattern 2: Numbers before "ل.س" (without space)
        pattern2 = r'(\d+(?:\.\d+)?)ل\.س'
        match2 = re.search(pattern2, balance_text)
        if match2:
            return float(match2.group(1))

        # If no pattern matches, return -1 to indicate parsing failure
        log(f"Could not extract debt amount from text: '{balance_text}'", "ERROR")
        return -1.0

    except Exception as e:
        log(f"Error extracting debt amount from text '{balance_text}': {e}", "ERROR")
        return -1.0

def validate_debt_payment(debt_amount, payment_amount):
    """
    Validate payment against debt amount based on new rules
    Args:
        debt_amount: Subscriber's debt amount
        payment_amount: Amount to be paid
    Returns:
        tuple: (is_valid, reason_message)
    """
    try:
        debt_amount = float(debt_amount)
        payment_amount = float(payment_amount)

        # Calculate absolute difference
        difference = abs(debt_amount - payment_amount)

        # Rule 1: If absolute difference <= 140 → accept (except when debt is zero)
        if difference <= 140 and debt_amount != 0:
            return True, f"تم قبول الدفع - الفرق ضمن الحد المسموح"

        # Rule 2: If debt <= payment amount → accept
        if debt_amount <= payment_amount:
            return True, f"تم قبول الدفع - المبلغ كافي لتغطية الدين"

        # Rule 3: If debt > payment amount and difference > 140 → reject
        if debt_amount > payment_amount:
            return False, f"المبلغ المطلوب: {debt_amount:.0f} ليرة سورية"

        return False, f"المبلغ المطلوب: {debt_amount:.0f} ليرة سورية"

    except Exception as e:
        log(f"Error validating debt payment: {e}", "ERROR")
        return True, "خطأ في التحقق من الدين - السماح بالدفع"

def process_single_payment(driver, order, processed_order_ids, retry_with_extra=False):
    """
    Process a single payment order with enhanced error handling and validation
    Args:
        driver: Selenium WebDriver instance
        order: Order data from API
        processed_order_ids: Set of already processed order IDs
        retry_with_extra: Whether to add 1 to amount for duplicate handling
    Returns:
        bool: True if payment was processed successfully, False otherwise
    """
    order_type = order.get("type", "")
    number = order["number"]
    order_id = str(order.get("id"))
    original_amount = order["amount"]

    # Add 1 to amount if this is a retry for duplicate handling
    amount = str(int(original_amount) + 1) if retry_with_extra else original_amount

    log(f"Processing order ID: {order_id}, Phone: {number}, Amount: {amount}", "INFO")

    try:
        # Navigate to payment page
        log("Navigating to payment page", "INFO")
        driver.get("http://sp.sawaisp.sy/charge_balance_to_customer")
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter phone number
        log(f"Entering phone number: {number}", "INFO")
        phone_input = driver.find_element(By.NAME, "phone_number")
        phone_input.click()
        random_sleep(0.3, 0.8)
        phone_input.clear()
        phone_input.send_keys(number)
        random_sleep()

        # Select region (041 Lattakia)
        log("Selecting region: 041 (Lattakia)", "INFO")
        driver.find_element(By.CLASS_NAME, "select2-choice").click()
        random_sleep(0.5, 1.0)
        driver.find_element(By.ID, "s2id_autogen1_search").send_keys("041 (اللاذقية)")
        random_sleep(1.0, 1.5)
        driver.find_element(By.XPATH, "//*[text()='041 (اللاذقية)']").click()
        random_sleep()

        # Check if number exists in system
        try:
            error_element = WebDriverWait(driver, 3).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/section/div[2]/div[3]/div/span"))
            )
            error_text = error_element.text.strip()

            if "لا يوجد بيانات مطابقة" in error_text:
                log(f"Number not found in system: {number}", "ERROR")
                log_payment_details(order_id, number, amount, "2", "Number not found in system")

                # Send status 2 to API (number not found) with Arabic message
                send_status_to_api(order_id, 2, "الرقم غير موجود في النظام")

                # Mark as processed
                with open("processed_orders.txt", "a", encoding="utf-8") as f:
                    f.write(order_id + "\n")
                processed_order_ids.add(order_id)
                return False

        except:
            # No error message found, continue with payment
            pass

        # Check package price for MB orders (speed validation)
        if "MB" in order_type:
            log("Checking package price for MB order", "INFO")
            try:
                price_element = WebDriverWait(driver, 10).until(
                    lambda d: d.find_element(By.ID, "member_pkg_price") if d.find_element(By.ID,"member_pkg_price").text.strip() != "" else False
                )
                price_text = price_element.text.strip()
                price_digits = ''.join(filter(str.isdigit, price_text))
                log(f"Package price displayed: {price_text} → Extracted amount: {price_digits}", "INFO")

                # Check if paid speed is less than package speed (reject only if paid < package)
                if int(original_amount) < int(price_digits):
                    log(f"Payment rejected: Paid amount ({original_amount}) is less than package price ({price_digits})", "WARNING")
                    log_payment_details(order_id, number, amount, "3", f"Paid amount less than package price: {original_amount} < {price_digits}")

                    # Send status 3 to API (payment failed due to wrong amount) with Arabic message
                    send_status_to_api(order_id, 3, f"المبلغ المدفوع ({original_amount}) أقل من سعر الحزمة ({price_digits})")

                    # Mark as processed
                    with open("processed_orders.txt", "a", encoding="utf-8") as f:
                        f.write(order_id + "\n")
                    processed_order_ids.add(order_id)
                    return False
                else:
                    log(f"Payment accepted: Paid amount ({original_amount}) >= package price ({price_digits})", "SUCCESS")

            except Exception as e:
                log(f"Error checking package price: {e}", "ERROR")

        # Check for amount over 100,000 limit
        if int(original_amount) > 100000:
            log(f"Payment rejected: Amount exceeds 100,000 limit ({original_amount})", "WARNING")
            log_payment_details(order_id, number, amount, "3", f"Amount exceeds 100k limit: {original_amount}")

            # Send status 3 to API (payment failed due to wrong amount) with Arabic message
            send_status_to_api(order_id, 3, f"المبلغ ({original_amount}) يتجاوز الحد الأقصى المسموح (100,000)")

            # Mark as processed
            with open("processed_orders.txt", "a", encoding="utf-8") as f:
                f.write(order_id + "\n")
            processed_order_ids.add(order_id)
            return False

        # Check subscriber balance and debt status
        try:
            balance_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "member_balance"))
            )
            balance_text = balance_element.text.strip()
            log(f"Subscriber balance info: {balance_text}", "INFO")

            # Check debt status - only validate if subscriber has debt ("عليه")
            if "عليه" in balance_text:
                # Extract debt amount from balance text
                debt_amount = extract_debt_amount(balance_text)
                log(f"Extracted debt amount: {debt_amount}", "INFO")

                # Check if debt extraction failed
                if debt_amount == -1.0:
                    log(f"Failed to extract debt amount from balance text: {balance_text}", "ERROR")
                    log_payment_details(order_id, number, amount, "3", f"Could not parse debt amount from: {balance_text}")

                    # Send status 3 to API with Arabic message
                    send_status_to_api(order_id, 3, "خطأ غير معروف في معالجة بيانات الدين")

                    # Mark as processed
                    with open("processed_orders.txt", "a", encoding="utf-8") as f:
                        f.write(order_id + "\n")
                    processed_order_ids.add(order_id)
                    return False

                # Validate payment against debt amount using new rules
                is_valid, reason = validate_debt_payment(debt_amount, original_amount)

                if not is_valid:
                    log(f"Payment rejected: {reason} - {balance_text}", "WARNING")
                    log_payment_details(order_id, number, amount, "4", f"Debt validation failed: {reason}")

                    # Send status 4 to API with Arabic message
                    send_status_to_api(order_id, 4, reason)

                    # Mark as processed
                    with open("processed_orders.txt", "a", encoding="utf-8") as f:
                        f.write(order_id + "\n")
                    processed_order_ids.add(order_id)
                    return False
                else:
                    log(f"Payment accepted: {reason} - {balance_text}", "SUCCESS")

            elif "له" in balance_text:
                log(f"Payment accepted: Subscriber has credit balance (company owes subscriber) - {balance_text}", "SUCCESS")
            else:
                log(f"Warning: Could not determine debt status from balance text: {balance_text}", "WARNING")

        except Exception as e:
            log(f"Error checking subscriber balance: {e}", "ERROR")
            # Continue with payment if balance check fails
            pass

        # Check if we can determine both package and debt information
        # If neither can be determined, the number might be invalid
        package_info_available = False
        debt_info_available = False

        try:
            # Check if package price is available (for MB orders)
            if "MB" in order_type:
                try:
                    price_element = driver.find_element(By.ID, "member_pkg_price")
                    if price_element.text.strip():
                        package_info_available = True
                except:
                    pass
            else:
                # For non-MB orders, assume package info is available
                package_info_available = True

            # Check if debt/balance info is available
            try:
                balance_element = driver.find_element(By.ID, "member_balance")
                balance_text = balance_element.text.strip()
                if balance_text and ("عليه" in balance_text or "له" in balance_text):
                    debt_info_available = True
            except:
                pass

            # If we cannot determine both package and debt info, number might be invalid
            if not package_info_available and not debt_info_available:
                log(f"Cannot determine both package and debt information for number {number} - number might be invalid", "ERROR")
                log_payment_details(order_id, number, amount, "2", "Cannot determine package and debt info - invalid number")

                # Send status 2 to API (number not found) with Arabic message
                send_status_to_api(order_id, 2, "خطأ الرقم أو الشركة")

                # Mark as processed
                with open("processed_orders.txt", "a", encoding="utf-8") as f:
                    f.write(order_id + "\n")
                processed_order_ids.add(order_id)
                return False

        except Exception as e:
            log(f"Error checking number validity: {e}", "ERROR")
            # Continue with payment if validity check fails
            pass

        # Check for duplicate payment within 24 hours
        if not retry_with_extra and check_duplicate_payment(number, original_amount):
            log(f"Duplicate payment detected for {number} with amount {original_amount} within 24 hours", "WARNING")
            log(f"Retrying with amount + 1: {int(original_amount) + 1}", "INFO")

            # Retry with amount + 1 (don't send API rejection yet)
            return process_single_payment(driver, order, processed_order_ids, retry_with_extra=True)

        # Get balance before payment
        log("Getting balance before payment", "INFO")
        li_element = driver.find_element(By.XPATH, "//li[contains(text(),'الرصيد')]")
        balance_before = li_element.text.split(":")[1].strip() if ":" in li_element.text else "0"
        log(f"Balance before payment: {balance_before}", "INFO")
        random_sleep()

        # Enter payment amount
        log(f"Entering payment amount: {amount}", "INFO")
        amount_input = driver.find_element(By.ID, "amount")
        amount_input.clear()
        amount_input.send_keys(amount)
        random_sleep()

        # Submit payment
        log("Submitting payment", "INFO")
        driver.find_element(By.ID, "charge_balance_to_customer_submitter").click()
        random_sleep()

        # Handle any alerts
        try:
            WebDriverWait(driver, 5).until(EC.alert_is_present())
            alert = Alert(driver)
            alert.accept()
            log("Alert dismissed", "INFO")
            random_sleep(0.5, 1)
        except:
            log("No alert present", "INFO")

        # Wait before checking result
        time.sleep(2)

        # Check for payment error messages before refreshing
        try:
            error_element = driver.find_element(By.XPATH, "/html/body/section/div[2]/div[3]/div/span")
            error_text = error_element.text.strip()

            if "خطأ في عملية شحن الرصيد،" in error_text:
                log(f"Payment error detected: {error_text}", "ERROR")

                if not retry_with_extra:
                    log("Retrying payment with amount + 1 due to payment error", "INFO")
                    return process_single_payment(driver, order, processed_order_ids, retry_with_extra=True)
                else:
                    log("Payment failed even with retry (+1 amount)", "ERROR")
                    log_payment_details(order_id, number, amount, "3", f"Payment error after retry: {error_text}")
                    send_status_to_api(order_id, 3, "فشل في عملية الدفع حتى بعد المحاولة الثانية")

                    # Mark as processed
                    with open("processed_orders.txt", "a", encoding="utf-8") as f:
                        f.write(order_id + "\n")
                    processed_order_ids.add(order_id)
                    return False
        except:
            # No error message found, continue
            pass

        # Refresh page and check balance after payment
        log("Refreshing page to check payment result", "INFO")
        driver.refresh()
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Get balance after payment
        li_element_after = driver.find_element(By.XPATH, "//li[contains(text(),'الرصيد')]")
        balance_after = li_element_after.text.split(":")[1].strip() if ":" in li_element_after.text else "0"
        log(f"Balance after payment: {balance_after}", "INFO")

        # Determine payment status
        payment_successful = balance_before != balance_after

        if payment_successful:
            log(f"Payment successful for order {order_id}", "SUCCESS")
            log_payment_details(order_id, number, amount, "1", f"Balance changed from {balance_before} to {balance_after}")
            # Send success status to API
            send_status_to_api(order_id, 1)
        else:
            log(f"Payment failed for order {order_id} - balance unchanged", "ERROR")

            # If this is the first attempt and balance didn't change, try with +1
            if not retry_with_extra:
                log("Payment failed - retrying with amount + 1", "INFO")
                return process_single_payment(driver, order, processed_order_ids, retry_with_extra=True)
            else:
                # This was already a retry, so send failure status
                log("Payment failed even with retry (+1 amount) - balance unchanged", "ERROR")
                log_payment_details(order_id, number, amount, "3", f"Balance unchanged after retry: {balance_before}")
                send_status_to_api(order_id, 3, "فشل الدفع - لم يتغير الرصيد حتى بعد المحاولة الثانية")

        # Mark order as processed
        with open("processed_orders.txt", "a", encoding="utf-8") as f:
            f.write(order_id + "\n")
        processed_order_ids.add(order_id)

        return payment_successful

    except Exception as e:
        log(f"Error processing payment for order {order_id}: {e}", "ERROR")
        log_payment_details(order_id, number, amount, "3", f"Processing error: {str(e)}")

        # Send status 3 to API (payment failed) with Arabic message
        send_status_to_api(order_id, 3, f"خطأ في معالجة الطلب: {str(e)}")

        # Mark as processed to avoid infinite retries
        with open("processed_orders.txt", "a", encoding="utf-8") as f:
            f.write(order_id + "\n")
        processed_order_ids.add(order_id)
        return False

def send_status_to_api(order_id, status, message=None):
    """
    Send payment status to API
    Args:
        order_id: Order ID
        status: Status code (1=paid, 2=number not found, 3=payment failed, 4=subscriber has debt)
        message: Optional Arabic message for rejection cases
    """
    try:
        status_payload = {
            "id": order_id,
            "status": status,
            "secret": secret
        }

        # Add Arabic message for rejection cases only (not for success)
        if message and status != 1:
            status_payload["message"] = message

        # Enhanced headers for API requests
        post_headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Origin": "https://menfax.com",
            "Referer": "https://menfax.com/",
            "Connection": "keep-alive"
        }

        log(f"Sending status {status} to API for order {order_id}", "INFO")
        status_response = requests.post(status_url, json=status_payload, headers=post_headers, timeout=10)

        log(f"API Response - Status Code: {status_response.status_code}", "INFO")
        log(f"API Response - Body: {status_response.text}", "INFO")

        if status_response.status_code == 200:
            log(f"Successfully updated order status to {status}", "SUCCESS")
        else:
            log(f"Failed to update order status - HTTP {status_response.status_code}", "ERROR")

    except requests.exceptions.RequestException as e:
        log(f"Failed to send status to API: {e}", "ERROR")

def process_orders():
    """
    Main function to process payment orders with enhanced error handling and validation
    """
    try:
        # Initialize browser with enhanced stealth settings
        log("Initializing browser with stealth settings", "INFO")
        ua = UserAgent()
        user_agent = ua.random

        options = Options()
        options.add_argument(f"user-agent={user_agent}")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-infobars")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1366,768")
        options.add_argument("--disable-web-security")
        options.add_argument("--allow-running-insecure-content")

        CHROMEDRIVER_PATH = "chromedriver.exe"
        service = Service(CHROMEDRIVER_PATH)
        driver = webdriver.Chrome(service=service, options=options)

        # Enhanced anti-detection measures
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'ar']});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
                Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4});
                window.chrome = {runtime: {}};
            """
        })

        # Login to the system
        log("Starting login process and browser initialization", "INFO")
        driver.get("http://sp.sawaisp.sy/charge_balance_to_customer")
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter credentials
        log("Entering username", "INFO")
        driver.find_element(By.NAME, "username").send_keys("fireware")
        random_sleep()

        log("Entering password", "INFO")
        driver.find_element(By.NAME, "password").send_keys("idhmhgauvhx")
        random_sleep()

        log("Clicking login button", "INFO")
        driver.find_element(By.XPATH, "/html/body/section/div/div[1]/div[2]/form/input[5]").click()
        WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.ID, "select2-chosen-1")))
        log("Login successful and system ready", "SUCCESS")
        random_sleep()

        # Load processed orders
        log("Loading processed orders list", "INFO")
        if os.path.exists("processed_orders.txt"):
            with open("processed_orders.txt", "r", encoding="utf-8") as f:
                processed_order_ids = set(line.strip() for line in f)
            log(f"Loaded {len(processed_order_ids)} previously processed orders", "INFO")
        else:
            processed_order_ids = set()
            log("No previous processed orders found", "INFO")

        # Server check variables
        last_server_check = time.time()
        server_check_interval = 3600  # Check server every hour (3600 seconds)

        log("Starting main processing loop", "INFO")
        while True:
            try:
                # Periodic server status check
                current_time = time.time()
                if current_time - last_server_check >= server_check_interval:
                    is_active_expiry = server_config.get('is_active_expiry_date', True)
                    if not is_active_expiry:
                        last_server_check = current_time
                        continue

                    if not check_server_status():
                        break
                    last_server_check = current_time

                # Fetch orders from API
                log("Fetching orders from API", "INFO")
                ua = UserAgent()
                headers = {
                    "User-Agent": ua.random,
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9,ar;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Connection": "keep-alive",
                    "Content-Type": "application/json",
                    "Cache-Control": "no-cache",
                    "Pragma": "no-cache",
                    "DNT": "1",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "cross-site",
                    "X-Requested-With": "XMLHttpRequest"
                }

                response = requests.get(url, headers=headers, timeout=10, verify=False)
                if response.status_code == 200:
                    orders = response.json()
                    filtered_orders = [order for order in orders if order.get("product") == 29]
                    new_orders = [order for order in filtered_orders if str(order["id"]) not in processed_order_ids]

                    log(f"Found {len(new_orders)} new orders to process", "INFO")

                    # Process each new order using the enhanced payment function
                    for order in new_orders:
                        log(f"Starting to process order ID: {order.get('id')}", "INFO")
                        success = process_single_payment(driver, order, processed_order_ids)

                        if success:
                            log(f"Order {order.get('id')} processed successfully", "SUCCESS")
                        else:
                            log(f"Order {order.get('id')} processing failed or rejected", "WARNING")

                        # Wait between orders to avoid overwhelming the system
                        random_sleep(1.5, 3.0)

                else:
                    log(f"Failed to fetch orders - HTTP {response.status_code}", "ERROR")

            except Exception as e:
                log(f"Error in main processing loop: {e}", "ERROR")

            # Wait before next iteration
            log("Waiting 10 seconds before next check", "INFO")
            time.sleep(10)

    except Exception as e:
        log(f"Critical error in main execution: {e}", "ERROR")
    finally:
        try:
            if 'driver' in locals() and driver is not None:
                # Try to close all windows first
                try:
                    driver.close()
                    log("Browser windows closed", "INFO")
                except:
                    pass

                # Then quit the driver
                driver.quit()
                log("Browser closed successfully", "INFO")
            else:
                log("Browser was not initialized or already closed", "INFO")
        except Exception as e:
            log(f"Error closing browser: {str(e)}", "ERROR")
            # Force kill chromedriver processes if needed
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name']):
                    if 'chromedriver' in proc.info['name'].lower():
                        proc.kill()
                        log("Force killed chromedriver process", "WARNING")
            except:
                pass

if __name__ == "__main__":
    process_orders()

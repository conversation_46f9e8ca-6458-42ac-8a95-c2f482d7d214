import requests
import time
import urllib3
import re
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.alert import Alert
from fake_useragent import UserAgent
import random
import os
import threading
import subprocess
import uuid
import sys


# تعطيل تحذيرات SSL الغير مهمة
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# دالة محسّنة للحصول على UUID الحقيقي للجهاز
def get_hardware_id():
    try:
        command = 'powershell "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"'
        result = subprocess.check_output(command, shell=True, stderr=subprocess.DEVNULL)
        hardware_id = result.decode().strip()
        if hardware_id and hardware_id != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF":
            return hardware_id
    except Exception:
        pass
    return str(uuid.getnode())

# دالة التحقق من الترخيص والأجهزة المصرح بها من السيرفر
def verify_license_and_device():
    try:
        device_id = get_hardware_id()

        # إعداد هيدرز متقدمة لتجنب رفض الطلبات
        ua = UserAgent()
        headers = {
            'User-Agent': ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Pragma': 'no-cache'
        }

        # Server connection with retry
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(
                    'https://yz9cz.github.io/verify_api/config.json',
                    headers=headers,
                    timeout=10,
                    verify=False,
                    allow_redirects=True
                )

                if response.status_code == 200:
                    config = response.json()
                    break
                else:
                    if attempt == max_retries - 1:
                        raise Exception("Connection failed")
                    time.sleep(2)

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:
                    raise Exception("Connection failed")
                time.sleep(2)

        # Check general activation status
        if not config.get('is_active', False):
            print(config.get('message', 'Program is currently unavailable'))
            sys.exit()

        # Check license expiry date
        expiry = config.get('expiry_date')
        if expiry:
            try:
                expiry_date = datetime.strptime(expiry, "%Y-%m-%d").date()
                today = datetime.now().date()
                if today > expiry_date:
                    print(config.get('message', 'Program access has expired'))
                    sys.exit()
            except ValueError:
                print(config.get('message', 'Configuration error occurred'))
                sys.exit()

        # Check authorized devices
        authorized_devices = config.get('authorized_devices', [])
        if not authorized_devices:
            print(config.get('message', 'No authorized access found'))
            sys.exit()

        device_authorized = False
        for authorized_device in authorized_devices:
            if device_id.lower() == authorized_device.lower():
                device_authorized = True
                break

        if not device_authorized:
            print(config.get('unauthorized_device_message', 'This device is not authorized. Please contact support for access.'))
            sys.exit()

        # Silent initialization - no success messages

        return config

    except Exception as e:
        print("This device is not authorized. Please contact support for access.")
        sys.exit()

# دالة للتحقق من حالة الإيقاف من السيرفر أثناء التشغيل
def check_server_status():
    try:
        # التحقق من حالة تفعيل فحص السيرفر
        is_active_expiry = server_config.get('is_active_expiry_date', True)
        if not is_active_expiry:
            # فحص السيرفر معطل، نستمر في التشغيل بدون فحص
            return True

        ua = UserAgent()
        headers = {
            'User-Agent': ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        }

        response = requests.get(
            'https://yz9cz.github.io/verify_api/config.json',
            headers=headers,
            timeout=5,
            verify=False
        )

        if response.status_code == 200:
            config = response.json()

            # التحقق من حالة تفعيل فحص السيرفر من الاستجابة الجديدة
            is_active_expiry_new = config.get('is_active_expiry_date', True)
            if not is_active_expiry_new:
                # فحص السيرفر معطل، نستمر في التشغيل بدون فحص
                return True

            if not config.get('is_active', False):
                print(config.get('message', 'Program is currently unavailable'))
                return False
        return True
    except:
        # في حالة فشل الاتصال، نستمر في التشغيل
        return True

# Run initial verification
server_config = verify_license_and_device()

# API
url = "https://menfax.com/pos/public/api/orders"
status_url = "https://menfax.com/pos/public/api/change-order-status"
secret = "SECRET1265AQREFGHKLFS!@#"

log_lock = threading.Lock()

# Enhanced logging system with detailed tracking
def log(message, log_type="INFO"):
    """
    Enhanced logging function with multiple log files and detailed tracking
    Args:
        message: The message to log
        log_type: Type of log (INFO, ERROR, SUCCESS, WARNING, PAYMENT)
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    full_message = f"[{timestamp}] [{log_type}] {message}"

    # Print to console in English
    print(full_message)

    # Write to main log file
    with log_lock:
        with open("syriatel_main_log.txt", "a", encoding="utf-8") as f:
            f.write(full_message + "\n")

        # Write to specific log files based on type
        if log_type == "ERROR":
            with open("syriatel_error_log.txt", "a", encoding="utf-8") as f:
                f.write(full_message + "\n")
        elif log_type == "PAYMENT":
            with open("syriatel_payment_log.txt", "a", encoding="utf-8") as f:
                f.write(full_message + "\n")
        elif log_type == "SUCCESS":
            with open("syriatel_success_log.txt", "a", encoding="utf-8") as f:
                f.write(full_message + "\n")

def log_payment_details(order_id, phone_number, amount, status, details=""):
    """
    Log detailed payment information to a dedicated payment tracking file
    Args:
        order_id: Order ID
        phone_number: Customer phone number
        amount: Payment amount
        status: Payment status (1=paid, 2=number not found, 3=payment failed)
        details: Additional details about the payment
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    payment_entry = f"{timestamp}|{order_id}|{phone_number}|{amount}|{status}|{details}\n"

    with log_lock:
        with open("syriatel_payment_tracking.txt", "a", encoding="utf-8") as f:
            f.write(payment_entry)

def random_sleep(min_sec=0.5, max_sec=1.5):
    """
    Sleep for a random duration between min_sec and max_sec seconds
    Args:
        min_sec: Minimum sleep duration
        max_sec: Maximum sleep duration
    """
    time.sleep(random.uniform(min_sec, max_sec))

def determine_payment_type(order):
    """
    Determine payment type based on order data
    Args:
        order: Order data from API
    Returns:
        str: 'bulk' for رصيد جملة, 'cash' for رصيد كاش, None if not supported
    """
    product = order.get("product")
    order_type = order.get("type", "")

    if product == 25:
        if "رصيد وحدات - Syriatel" in order_type:
            return "bulk"
        elif "رصيد كاش - Syriatel" in order_type:
            return "cash"

    return None

def get_payment_config(payment_type):
    """
    Get configuration for specific payment type
    Args:
        payment_type: 'bulk' or 'cash'
    Returns:
        dict: Configuration with URL and max_amount
    """
    configs = {
        "bulk": {
            "url": "https://abili.syriatel.com.sy/Transfer.aspx",
            "max_amount": 1000000,
            "name": "رصيد جملة"
        },
        "cash": {
            "url": "https://abili.syriatel.com.sy/ePaymentTransfer.aspx",
            "max_amount": 10000000,
            "name": "رصيد كاش"
        }
    }
    return configs.get(payment_type)

def process_single_payment(driver, order, processed_order_ids):
    """
    Process a single payment order for Syriatel
    Args:
        driver: Selenium WebDriver instance
        order: Order data from API
        processed_order_ids: Set of already processed order IDs
    Returns:
        bool: True if payment was processed successfully, False otherwise
    """
    order_type = order.get("type", "")
    number = order["number"]
    order_id = str(order.get("id"))
    amount = order["amount"]

    log(f"Processing order ID: {order_id}, Phone: {number}, Amount: {amount}, Type: {order_type}", "INFO")

    # Determine payment type
    payment_type = determine_payment_type(order)
    if not payment_type:
        log(f"Unsupported order type: {order_type}", "ERROR")
        log_payment_details(order_id, number, amount, "2", f"Unsupported order type: {order_type}")
        send_status_to_api(order_id, 2, "نوع الطلب غير مدعوم")

        with open("syriatel_processed_orders.txt", "a", encoding="utf-8") as f:
            f.write(order_id + "\n")
        processed_order_ids.add(order_id)
        return False

    # Get payment configuration
    config = get_payment_config(payment_type)
    log(f"Payment type: {config['name']}, Max amount: {config['max_amount']}", "INFO")

    # Check amount limit
    if int(amount) > config['max_amount']:
        log(f"Payment rejected: Amount {amount} exceeds limit {config['max_amount']}", "WARNING")
        log_payment_details(order_id, number, amount, "2", f"Amount exceeds limit: {amount} > {config['max_amount']}")
        send_status_to_api(order_id, 2, f"المبلغ ({amount}) يتجاوز الحد الأقصى المسموح ({config['max_amount']})")

        with open("syriatel_processed_orders.txt", "a", encoding="utf-8") as f:
            f.write(order_id + "\n")
        processed_order_ids.add(order_id)
        return False

    try:
        # Navigate to payment page
        log(f"Navigating to {config['name']} page", "INFO")
        driver.get(config['url'])
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter customer number/code
        log(f"Entering customer number: {number}", "INFO")
        customer_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[1]/input[1]"))
        )
        customer_input.clear()
        customer_input.send_keys(number)
        random_sleep()

        # Enter amount
        log(f"Entering amount: {amount}", "INFO")
        amount_input = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[3]/input")
        amount_input.clear()
        amount_input.send_keys(amount)
        random_sleep()

        # Click first button to check balance
        log("Clicking check balance button", "INFO")
        check_button = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[5]/input")
        check_button.click()
        random_sleep(2)

        # Get balance before payment
        log("Getting balance before payment", "INFO")
        try:
            balance_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/form/div[3]/div/div/div[2]/div[2]/table/tbody/tr/td[3]"))
            )
            balance_before = balance_element.text.strip()
            log(f"Balance before payment: {balance_before}", "INFO")
        except Exception as e:
            log(f"Could not get balance before payment: {e}", "ERROR")
            log_payment_details(order_id, number, amount, "2", f"Could not get balance: {str(e)}")
            send_status_to_api(order_id, 2, "لا يمكن الحصول على رصيد المشترك")

            with open("syriatel_processed_orders.txt", "a", encoding="utf-8") as f:
                f.write(order_id + "\n")
            processed_order_ids.add(order_id)
            return False

        # Click confirm payment button
        log("Clicking confirm payment button", "INFO")
        confirm_button = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/table/tbody/tr[4]/td/p/input[2]")
        confirm_button.click()
        random_sleep()

        # Handle JavaScript alert
        try:
            WebDriverWait(driver, 5).until(EC.alert_is_present())
            alert = Alert(driver)
            alert.accept()
            log("JavaScript alert accepted", "INFO")
            random_sleep(2)
        except:
            log("No JavaScript alert present", "INFO")

        # Check if redirected to error page (for cash payments)
        current_url = driver.current_url
        if "Error.aspx" in current_url:
            log("Agent does not exist - redirected to error page", "ERROR")
            log_payment_details(order_id, number, amount, "2", "Agent does not exist")
            send_status_to_api(order_id, 2, "Agent does not exist")

            with open("syriatel_processed_orders.txt", "a", encoding="utf-8") as f:
                f.write(order_id + "\n")
            processed_order_ids.add(order_id)
            return False

        # Get balance after payment
        log("Getting balance after payment", "INFO")
        try:
            balance_element_after = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/form/div[3]/div/div/div[2]/div[2]/table/tbody/tr/td[3]"))
            )
            balance_after = balance_element_after.text.strip()
            log(f"Balance after payment: {balance_after}", "INFO")
        except Exception as e:
            log(f"Could not get balance after payment: {e}", "ERROR")
            balance_after = balance_before

        # Get operation message
        try:
            message_element = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[6]/span")
            operation_message = message_element.text.strip()
            log(f"Operation message: {operation_message}", "INFO")
        except:
            operation_message = "لا توجد رسالة"
            log("No operation message found", "WARNING")

        # Check if message contains pattern with three numbers in parentheses
        numbers_in_parentheses = re.findall(r'\(([0-9]+(?:\.[0-9]+)?)\)', operation_message)

        if len(numbers_in_parentheses) >= 3:
            # Take the first number and create simplified message
            first_number = numbers_in_parentheses[0]
            simplified_message = f"المبلغ المطلوب {first_number}"
        else:
            # Keep original message if pattern doesn't match
            simplified_message = operation_message

        # Determine payment success by comparing balances
        try:
            # Remove commas and convert to float safely
            balance_before_clean = balance_before.replace(',', '').replace(' ', '')
            balance_after_clean = balance_after.replace(',', '').replace(' ', '')

            balance_before_num = float(balance_before_clean)
            balance_after_num = float(balance_after_clean)
            expected_balance = balance_before_num - float(amount)

            # Check if balance changed by the expected amount (with small tolerance)
            if abs(balance_after_num - expected_balance) < 1:
                log(f"Payment successful for order {order_id}", "SUCCESS")
                log_payment_details(order_id, number, amount, "1", f"Balance changed from {balance_before} to {balance_after}")
                send_status_to_api(order_id, 1, simplified_message)
                payment_successful = True
            else:
                log(f"Payment failed for order {order_id} - balance not changed correctly", "ERROR")
                log_payment_details(order_id, number, amount, "2", f"Balance unchanged: {balance_before} -> {balance_after}")
                send_status_to_api(order_id, 2, simplified_message)
                payment_successful = False

        except Exception as e:
            log(f"Error comparing balances: {e}", "ERROR")
            log_payment_details(order_id, number, amount, "2", f"Balance comparison error: {str(e)}")
            send_status_to_api(order_id, 2, simplified_message)
            payment_successful = False

        # Mark order as processed
        with open("syriatel_processed_orders.txt", "a", encoding="utf-8") as f:
            f.write(order_id + "\n")
        processed_order_ids.add(order_id)

        return payment_successful

    except Exception as e:
        log(f"Error processing payment for order {order_id}: {e}", "ERROR")
        log_payment_details(order_id, number, amount, "2", f"Processing error: {str(e)}")
        send_status_to_api(order_id, 2, f"خطأ في معالجة الطلب: {str(e)}")

        with open("syriatel_processed_orders.txt", "a", encoding="utf-8") as f:
            f.write(order_id + "\n")
        processed_order_ids.add(order_id)
        return False

def send_status_to_api(order_id, status, message=None):
    """
    Send payment status to API
    Args:
        order_id: Order ID
        status: Status code (1=success, 2=failure)
        message: Optional message
    """
    try:
        status_payload = {
            "id": order_id,
            "status": status,
            "secret": secret
        }

        # Add message for both success and failure cases
        if message:
            status_payload["message"] = message

        # Enhanced headers for API requests
        post_headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Origin": "https://menfax.com",
            "Referer": "https://menfax.com/",
            "Connection": "keep-alive"
        }

        log(f"Sending status {status} to API for order {order_id}", "INFO")
        status_response = requests.post(status_url, json=status_payload, headers=post_headers, timeout=10)

        log(f"API Response - Status Code: {status_response.status_code}", "INFO")
        log(f"API Response - Body: {status_response.text}", "INFO")

        if status_response.status_code == 200:
            log(f"Successfully updated order status to {status}", "SUCCESS")
        else:
            log(f"Failed to update order status - HTTP {status_response.status_code}", "ERROR")

    except requests.exceptions.RequestException as e:
        log(f"Failed to send status to API: {e}", "ERROR")

def process_orders():
    """
    Main function to process payment orders for Syriatel
    """
    try:
        # Initialize browser with enhanced stealth settings
        log("Initializing browser with stealth settings", "INFO")
        ua = UserAgent()
        user_agent = ua.random

        options = Options()
        options.add_argument(f"user-agent={user_agent}")
        options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-infobars")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1366,768")
        options.add_argument("--disable-web-security")
        options.add_argument("--allow-running-insecure-content")
        options.add_argument("--disable-logging")
        options.add_argument("--log-level=3")
        options.add_argument("--silent")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-background-networking")
        options.add_argument("--disable-sync")
        options.add_argument("--disable-translate")
        options.add_argument("--disable-features=VizDisplayCompositor,TranslateUI")
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")

        # إعدادات إضافية لإخفاء الرسائل
        options.add_experimental_option("prefs", {
            "profile.default_content_setting_values.notifications": 2
        })

        # Use webdriver-manager to automatically download and manage ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)

        # Enhanced anti-detection measures
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'ar']});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
                Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4});
                window.chrome = {runtime: {}};
            """
        })

        # Login to Syriatel system
        log("Starting login process to Syriatel", "INFO")
        driver.get("https://abili.syriatel.com.sy/Login.aspx")
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter credentials
        log("Entering username", "INFO")
        username_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "UsernameTextBox"))
        )
        username_field.clear()
        username_field.send_keys("WSLRETCOS310")
        random_sleep()

        log("Entering password", "INFO")
        password_field = driver.find_element(By.ID, "PasswordTextBox")
        password_field.clear()
        password_field.send_keys("Ea@456NN")
        random_sleep()

        log("Clicking login button", "INFO")
        submit_button = driver.find_element(By.ID, "SubmitButton")
        submit_button.click()

        # Wait for login to complete
        time.sleep(5)
        current_url = driver.current_url
        if "Login.aspx" not in current_url:
            log("Login successful to Syriatel system", "SUCCESS")
        else:
            log("Login failed to Syriatel system", "ERROR")
            return

        # Load processed orders
        log("Loading processed orders list", "INFO")
        if os.path.exists("syriatel_processed_orders.txt"):
            with open("syriatel_processed_orders.txt", "r", encoding="utf-8") as f:
                processed_order_ids = set(line.strip() for line in f)
            log(f"Loaded {len(processed_order_ids)} previously processed orders", "INFO")
        else:
            processed_order_ids = set()
            log("No previous processed orders found", "INFO")

        # Server check variables
        last_server_check = time.time()
        server_check_interval = 3600  # Check server every hour

        log("Starting main processing loop for Syriatel orders", "INFO")
        while True:
            try:
                # Periodic server status check
                current_time = time.time()
                if current_time - last_server_check >= server_check_interval:
                    is_active_expiry = server_config.get('is_active_expiry_date', True)
                    if not is_active_expiry:
                        last_server_check = current_time
                        continue

                    if not check_server_status():
                        break
                    last_server_check = current_time

                # Fetch orders from API
                log("Fetching orders from API", "INFO")
                ua = UserAgent()
                headers = {
                    "User-Agent": ua.random,
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "en-US,en;q=0.9,ar;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Connection": "keep-alive",
                    "Content-Type": "application/json",
                    "Cache-Control": "no-cache",
                    "Pragma": "no-cache",
                    "DNT": "1",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "cross-site",
                    "X-Requested-With": "XMLHttpRequest"
                }

                response = requests.get(url, headers=headers, timeout=10, verify=False)
                if response.status_code == 200:
                    orders = response.json()
                    # Filter for Syriatel orders (product 25)
                    filtered_orders = [order for order in orders if order.get("product") == 25]
                    new_orders = [order for order in filtered_orders if str(order["id"]) not in processed_order_ids]

                    log(f"Found {len(new_orders)} new Syriatel orders to process", "INFO")

                    # Process each new order
                    for order in new_orders:
                        log(f"Starting to process order ID: {order.get('id')}", "INFO")
                        success = process_single_payment(driver, order, processed_order_ids)

                        if success:
                            log(f"Order {order.get('id')} processed successfully", "SUCCESS")
                        else:
                            log(f"Order {order.get('id')} processing failed or rejected", "WARNING")

                        # Wait between orders to avoid overwhelming the system
                        random_sleep(2.0, 4.0)

                else:
                    log(f"Failed to fetch orders - HTTP {response.status_code}", "ERROR")

            except Exception as e:
                log(f"Error in main processing loop: {e}", "ERROR")

            # Wait before next iteration
            log("Waiting 10 seconds before next check", "INFO")
            time.sleep(10)

    except Exception as e:
        log(f"Critical error in main execution: {e}", "ERROR")
    finally:
        try:
            if 'driver' in locals() and driver is not None:
                driver.quit()
                log("Browser closed successfully", "INFO")
        except Exception as e:
            log(f"Error closing browser: {str(e)}", "ERROR")

if __name__ == "__main__":
    process_orders()
